import { colors, dark, flex, light, textSizes, breakpoints } from "@styles/vars";
import styled from "styled-components/macro";
import { colorTypes } from "@constants/colors";
import theme from "styled-theming";
import { ModalContent } from "@components/ModalWindow";

const disabled = (theme) => (theme === "dark" ? light.text : "#DCE2E8");

const available = (theme) => (theme === "dark" ? dark.bodyBg : light.bodyBg);

export const StyledEvent = styled.div`
  background-color: ${(props) =>
    props.type !== "disabled" &&
    props.type !== "available" &&
    colors[colorTypes.find((color) => color?.cat === props?.type)?.color]};
  transition: all var(--transition);
  position: relative;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 5;
  }

  .cover {
    display: flex;
    ${flex.center};
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: ${colors.blue};
    color: #fff;
    z-index: 30;
    visibility: hidden;
    opacity: 0;
    transition: opacity var(--transition), visibility var(--transition);
    font-size: ${textSizes["16"]};
  }

  ${(props) =>
    props.type === "disabled" &&
    `
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
    transform: scaleX(-1);
    background-size: 10px 10px;
    background-image: repeating-linear-gradient(45deg, ${disabled(props.mode)} 0,
     ${disabled(props.mode)} 1px, transparent 0, transparent 50%);
     pointer-events: none;
  `};

  ${(props) =>
    props.type === "available" &&
    `
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
    background-color: ${available(props.mode)};
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
  `};

  &:after {
    content: "";
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    width: 100%;
    height: calc(100% - 1px);
    mix-blend-mode: luminosity;
    background-color: ${theme("theme", {
      light: light.bodyBg,
      dark: dark.bodyBg,
    })};
    opacity: ${theme("theme", {
      light: 1,
      dark: 0.8,
    })};
    display: ${(props) => (props.type !== "disabled" && props.type !== "available" ? "block" : "none")};
  }

  &.isEnded {
    background-color: ${(props) => props.type !== "disabled" && props.type !== "available" && colors.gray};
    opacity: ${(props) => props.type !== "disabled" && props.type !== "available" && 0.5};
  }

  .icon {
    color: ${colors.blue};
  }

  .event-title {
    font-size: ${textSizes["12"]};
    position: relative;
    z-index: 2;
    color: ${theme("theme", {
      light: light.text,
      dark: "#fff",
    })};
    line-height: 1.3;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    // padding-right: 24px; /* Make space for the count badge */
  }

  .event-status {
    font-size: ${textSizes["8"]};
    position: relative;
    z-index: 2;
    display: flex;
    line-height: 1.3;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    justify-content: center;
    white-space: nowrap;
    text-transform: capitalize;
    // padding-right: 24px; /* Make space for the count badge */

    /* Responsive font sizes for small screens */
    @media (max-width: 480px) {
      font-size: ${textSizes["8"]};
    }

    @media (max-width: 320px) {
      font-size: ${textSizes["8"]};
    }
  }

  .appointment-count {
    position: absolute;
    top: 4px;
    right: 4px;
    font-size: ${textSizes["9"]};
    font-weight: 600;
    color: #fff;
    // background-color: ${colors.blue};
    padding: 2px 5px;
    border-radius: 8px;
    line-height: 1;
    z-index: 3;
    min-width: 16px;
    text-align: center;
    // box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: ${colors.blue};
      transform: scale(1.1);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    }
  }

  .event-time {
    font-size: ${textSizes["10"]};
    opacity: 0.8;
    margin-top: 2px;
    position: relative;
    z-index: 2;
    color: ${theme("theme", {
      light: light.text,
      dark: "#fff",
    })};
  }

  &.event-day {
    padding: 12px 16px;
    font-size: ${textSizes["14"]};
    min-height: 50px;
    height: 100%;
    ${flex.col};
    justify-content: flex-start;
    align-items: flex-start;
    text-align: left;
    border-left: 4px solid ${(props) =>
      props.type !== "disabled" &&
      props.type !== "available" &&
      colors[colorTypes?.find((color) => color?.cat === props?.type)?.color]};

    .event-title {
      font-size: ${textSizes["14"]};
      font-weight: 600;
      margin-bottom: 4px;
      white-space: normal;
      overflow: visible;
      text-overflow: unset;
      padding-right: 30px; /* More space for larger count badge in day view */
    }

    .event-status {
      font-size: ${textSizes["8"]};
      font-weight: 600;
      margin-bottom: 4px;
      white-space: normal;
      overflow: visible;
      text-overflow: unset;
      padding-right: 30px; /* More space for larger count badge in day view */

      @media (max-width: 480px) {
        font-size: ${textSizes["8"]};
      }

      @media (max-width: 320px) {
        font-size: ${textSizes["8"]};
      }
    }

    .event-time {
      font-size: ${textSizes["12"]};
      opacity: 0.7;
    }

    .appointment-count {
      top: 8px;
      right: 8px;
      font-size: ${textSizes["10"]};
      padding: 3px 6px;
      min-width: 18px;
      cursor: pointer;
    }
  }

  &.event-week,
  &.event-month {
    min-width: 100%;
    min-height: 28px;
    // padding: 8px 12px;
    border-radius: 4px;
    ${flex.col};
    justify-content: center;
    align-items: flex-start;

    &:after {
      display: ${(props) => (props.type !== "disabled" && props.type !== "available" ? "block" : "none")};
    }

    .event-title {
      font-size: ${textSizes["12"]};
      display: block;
      line-height: 1.3;
      max-width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-weight: 500;
      padding-right: 20px; /* Space for count badge */
    }

    .event-status {
      font-size: ${textSizes["12"]};
      display: block;
      line-height: 1.3;
      max-width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-weight: 600;
      padding-right: 20px; /* Space for count badge */

      @media (max-width: 768px) {
        font-size: ${textSizes["11"]};
      }

      @media (max-width: 480px) {
        font-size: ${textSizes["10"]};
      }

      @media (max-width: 320px) {
        font-size: ${textSizes["9"]};
      }
    }

    .event-time {
      display: block;
      font-size: ${textSizes["10"]};
      margin-top: 2px;
      opacity: 0.8;
    }

    .appointment-count {
      top: 6px;
      right: 6px;
      font-size: ${textSizes["8"]};
      padding: 1px 4px;
      min-width: 14px;
      cursor: pointer;
    }
  }

  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: inline-block;
  }

  ${breakpoints.tablet} {
    &.event-week,
    &.event-month {
      min-width: 100%;
      min-height: 30px;
      // padding: 10px 14px;

      .event-title {
        font-size: ${textSizes["13"]};
        font-weight: 500;
        padding-right: 22px; /* Space for count badge */
      }

      .event-status {
        font-size: ${textSizes["13"]};
        font-weight: 600;
        padding-right: 22px; /* Space for count badge */
      }

      .event-time {
        font-size: ${textSizes["11"]};
      }

      .appointment-count {
        top: 8px;
        right: 8px;
        font-size: ${textSizes["8"]};
        padding: 2px 4px;
        min-width: 15px;
        cursor: pointer;
      }
    }
  }

  ${breakpoints.laptop} {
    &.event-week,
    &.event-month {
      min-width: 100%;
      min-height: 32px;
      // padding: 10px 14px;
      ${flex.col};
      justify-content: center;
      align-items: flex-start;

      &:after {
        display: ${(props) => (props.type !== "disabled" && props.type !== "available" ? "block" : "none")};
      }

      .event-title {
        font-size: ${textSizes["13"]};
        display: block;
        max-width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 500;
        padding-right: 22px; /* Space for count badge */
      }

      .event-status {
        font-size: ${textSizes["13"]};
        display: block;
        max-width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 600;
        padding-right: 22px; /* Space for count badge */
      }

      .event-time {
        display: block;
        font-size: ${textSizes["11"]};
        margin-top: 2px;
      }
    }

    &.event-month {
      min-height: 36px;

      ${(props) =>
        props.type === "available" &&
        `
        padding: 10px 14px;
        min-height: 40px;
      `};

      .event-title {
        font-size: ${textSizes["13"]};
        font-weight: 600;
      }
    }
  }

  ${breakpoints.laptopL} {
    &.event-week,
    &.event-month {
      min-height: 39px;
      // padding: 12px 16px;
      padding-left:12px;

      .event-title {
        font-size: ${textSizes["14"]};
        font-weight: 500;
        padding-right: 26px; /* Space for count badge */
      }

      .event-status {
        font-size: ${textSizes["14"]};
        font-weight: 600;
        padding-right: 26px; /* Space for count badge */
      }

      .event-time {
        font-size: ${textSizes["12"]};
      }

      .appointment-count {
        top: 10px;
        right: 10px;
        font-size: ${textSizes["9"]};
        padding: 2px 5px;
        min-width: 16px;
        cursor: pointer;
      }
    }

    &.event-month {
      min-height: 40px;

      .event-title {
        font-size: ${textSizes["14"]};
        font-weight: 600;
        padding-right: 26px; /* Space for count badge */
      }

      .event-status {
        font-size: ${textSizes["14"]};
        font-weight: 600;
        padding-right: 26px; /* Space for count badge */
      }
    }
  }
`;

export const EventModal = styled(ModalContent)`
  ${flex.col};
  gap: 20px;
  position: relative;
  padding: 24px;
  border-radius: 12px;
  background-color: ${theme("theme", {
    light: light.widgetBg,
    dark: dark.widgetBg,
  })};
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);

  .block {
    ${flex.col};
    gap: 6px;

    &:first-of-type {
      .value {
        font-size: ${textSizes["16"]};
        font-weight: 600;
        color: ${theme("theme", {
          light: light.text,
          dark: dark.text,
        })};
      }
    }

    .label {
      font-size: ${textSizes["12"]};
      font-weight: 500;
      color: ${colors.gray};
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .value {
      font-size: ${textSizes["14"]};
      color: ${theme("theme", {
        light: light.text,
        dark: dark.text,
      })};
      line-height: 1.4;
    }
  }

  ${breakpoints.tablet} {
    max-width: 400px;
    margin: 0 auto;
    padding: 32px;

    .block {
      gap: 8px;

      &:first-of-type {
        .value {
          font-size: ${textSizes["18"]};
        }
      }

      .label {
        font-size: ${textSizes["13"]};
      }

      .value {
        font-size: ${textSizes["15"]};
      }
    }
  }

  .appointments-list {
    ${flex.col};
    gap: 12px;
    margin-top: 8px;
    max-height: 300px;
    overflow-y: auto;

    .appointment-item {
      display: flex;
      justify-content: space-between;
      gap: 12px;
      padding: 12px;
      border-radius: 8px;
      background-color: ${theme("theme", {
        light: "rgba(0, 0, 0, 0.02)",
        dark: "rgba(255, 255, 255, 0.05)",
      })};
      border: 1px solid ${theme("theme", {
        light: "rgba(0, 0, 0, 0.08)",
        dark: "rgba(255, 255, 255, 0.1)",
      })};
      align-items: center;
      transition: all 0.2s ease;

      &.clickable {
        cursor: pointer;

        &:hover {
          background-color: ${theme("theme", {
            light: "rgba(59, 130, 246, 0.08)",
            dark: "rgba(59, 130, 246, 0.15)",
          })};
          border-color: ${colors.blue + "40"};
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }

      ${breakpoints.mobileS} {
      font-size: ${textSizes["10"]};
      }

      .appointment-time {
        font-size: ${textSizes["12"]};
        font-weight: 600;
        color: ${colors.blue};
        white-space: nowrap;
      }

      .appointment-details {
        ${flex.col};
        gap: 2px;
        min-width: 0;

        .appointment-name {
          font-size: ${textSizes["13"]};
          font-weight: 500;
          color: ${theme("theme", {
            light: light.text,
            dark: dark.text,
          })};
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .appointment-type {
          font-size: ${textSizes["11"]};
          color: ${colors.gray};
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .appointment-status {
        font-size: ${textSizes["8"]};
        font-weight: 500;
            width: fit-content;
        padding: 4px 6px;
        border-radius: 12px;
        white-space: nowrap;
        text-transform: capitalize;
        /* Background and color will be set inline based on status */
      }
    }
  }

  ${breakpoints.tablet} {
    .appointments-list {
      .appointment-item {
        padding: 14px;
        gap: 14px;

        .appointment-time {
          font-size: ${textSizes["13"]};
        }

        .appointment-details {
          .appointment-name {
            font-size: ${textSizes["14"]};
          }

          .appointment-type {
            font-size: ${textSizes["12"]};
          }
        }

        .appointment-status {
          font-size: ${textSizes["8"]};
          padding: 5px 10px;

        }
      }
    }
  }
    .add-appointment-btn {
  margin-top: 1rem;
  padding: 10px 16px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  font-size: ${textSizes["14"]};
  text-align: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.add-appointment-btn:hover {
  background-color: #43a047;
}

`;
