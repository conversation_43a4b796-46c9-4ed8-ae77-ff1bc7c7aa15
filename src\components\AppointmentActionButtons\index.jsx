import React, { useState } from "react";
import { <PERSON>, Button, CircularProgress, Too<PERSON>ip, Typography } from "@mui/material";
import { CheckCircle, Cancel } from "@mui/icons-material";
import { useDispatch } from "react-redux";
import { useSnackbar } from "notistack";
import { useNavigate } from "react-router-dom";
import moment from "moment";
import ConfirmationModal from "@components/ConfirmationModal";
import { updateAppointmentStatus, deleteAppointment } from "@store/slices/appointments";
import { canCompleteAppointment, getAppointmentCompletionTooltip } from "@utils/dates";
import styled from "styled-components";
import { colors } from "@styles/vars";
const ActionButtonsContainer = styled(Box)`
  display: flex;
  gap: 12px;
  margin: 0;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    flex-direction: row;
    gap: 8px;
    justify-content: space-between;
    width: auto;
  }
`;

const ActionButton = styled(But<PERSON>)`
  min-width: 100px;
  padding: 6px 8px;
  font-weight: 600;
  border-radius: 8px;
  text-transform: none;
  font-size: 14px;

  @media (max-width: 768px) {
    min-width: auto;
    padding: 6px 8px;
    font-size: 13px;
  }

  @media (max-width: 600px) {
    flex: 1;
    min-width: unset;
  }
`;

const AppointmentActionButtons = ({ appointment, onActionComplete }) => {
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();
  const navigate = useNavigate();

  const [isCompleteModalOpen, setCompleteModalOpen] = useState(false);
  const [isCancelModalOpen, setCancelModalOpen] = useState(false);
  const [isCompleting, setCompleting] = useState(false);
  const [isCanceling, setCanceling] = useState(false);

  // Don't show buttons if appointment is already completed or cancelled
  
  if (appointment?.status === "COMPLETED") {
    return (
      <Typography
        sx={{
          pointerEvents: "none",
          backgroundColor: colors.green,
          borderRadius: "12px",
          padding: "5px 8px",
          aligntext: "center",
          alignContent: "center",
          marginRight: "auto",
          color: "white",
          fontSize: "14px",
        }}
      >
        Completed
      </Typography>
    );
  }

  // Check if appointment can be completed using utility function
  const canComplete = canCompleteAppointment(appointment?.startDateTime);

  // Get tooltip text using utility function
  const tooltipText = getAppointmentCompletionTooltip(appointment?.startDateTime);

  const handleMarkAsCompleted = async () => {
    // Double-check date validation before proceeding
    if (!canComplete) {
      enqueueSnackbar("Only today's appointments can be marked as completed", { variant: "error" });
      return;
    }

    setCompleting(true);
    try {
      const currentDateTime = new Date().toISOString();

      await dispatch(
        updateAppointmentStatus({
          appointmentId: appointment.id,
          status: "COMPLETED",
          endDate: currentDateTime,
        }),
      ).unwrap();

      enqueueSnackbar("Appointment marked as completed successfully", { variant: "success" });
      setCompleteModalOpen(false);

      // Refresh the page to show updated status
      // window.location.reload();

      if (onActionComplete) {
        onActionComplete("completed");
      }
    } catch (error) {
      console.error("Error marking appointment as completed:", error);
      enqueueSnackbar("Failed to mark appointment as completed", { variant: "error" });
    } finally {
      setCompleting(false);
    }
  };

  const handleCancelAppointment = async () => {
    setCanceling(true);
    try {
      await dispatch(deleteAppointment(appointment.id)).unwrap();

      enqueueSnackbar("Appointment cancelled successfully", { variant: "success" });
      setCancelModalOpen(false);

      // Navigate back to appointments list after cancellation
      navigate("/appointments");

      if (onActionComplete) {
        onActionComplete("cancelled");
      }
    } catch (error) {
      console.error("Error cancelling appointment:", error);
      enqueueSnackbar("Failed to cancel appointment", { variant: "error" });
    } finally {
      setCanceling(false);
    }
  };

  return (
    <>
      <ActionButtonsContainer>
        {appointment?.status === "SCHEDULED" && (
          <Typography
            sx={{
              pointerEvents: "none",
              backgroundColor: colors.yellow,
              borderRadius: "12px",
              padding: "5px 8px",
              aligntext: "center",
              alignContent: "center",
              marginRight: "auto",
              color: "white",
              fontSize: "14px",
            }}
          >
            Scheduled
          </Typography>
        )}
        {appointment?.status === "COMPLETED" && (
          <Typography
            sx={{
              pointerEvents: "none",
              backgroundColor: colors.green,
              borderRadius: "12px",
              padding: "5px 8px",
              aligntext: "center",
              alignContent: "center",
              marginRight: "auto",
              color: "white",
              fontSize: "14px",
            }}
          >
            Completed Green
          </Typography>
        )}
        <Tooltip title={tooltipText} arrow>
          <span>
            <ActionButton
              variant="contained"
              color="success"
              startIcon={<CheckCircle />}
              onClick={() => setCompleteModalOpen(true)}
              disabled={isCompleting || isCanceling || !canComplete}
            >
              {isCompleting ? <CircularProgress size={20} color="inherit" /> : "Complete"}
            </ActionButton>
          </span>
        </Tooltip>

        <ActionButton
          variant="contained"
          color="error"
          startIcon={<Cancel />}
          onClick={() => setCancelModalOpen(true)}
          disabled={isCompleting || isCanceling}
        >
          {isCanceling ? <CircularProgress size={20} color="inherit" /> : "Cancel"}
        </ActionButton>
      </ActionButtonsContainer>

      {/* Mark as Completed Confirmation Modal */}
      <ConfirmationModal
        isOpen={isCompleteModalOpen}
        onClose={() => setCompleteModalOpen(false)}
        onCancel={() => setCompleteModalOpen(false)}
        onConfirm={handleMarkAsCompleted}
        title="Mark Appointment as Completed"
        subtitle={
          canComplete
            ? `Are you sure you want to mark this appointment as completed?`
            : `This appointment is scheduled for ${moment(appointment?.startDateTime).format(
                "MMMM DD, YYYY",
              )}. Only today's appointments can be marked as completed.`
        }
        confirmText="Mark as Completed"
        cancelText="Cancel"
        isLoading={isCompleting}
      />

      {/* Cancel Appointment Confirmation Modal */}
      <ConfirmationModal
        isOpen={isCancelModalOpen}
        onClose={() => setCancelModalOpen(false)}
        onCancel={() => setCancelModalOpen(false)}
        onConfirm={handleCancelAppointment}
        title="Cancel Appointment"
        subtitle={`Are you sure you want to cancel this appointment? This action cannot be undone.`}
        confirmText="YES"
        cancelText="NO"
        isLoading={isCanceling}
      />
    </>
  );
};

export default AppointmentActionButtons;
