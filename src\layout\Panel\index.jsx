// styled components
import { Actions, Header, Input, Label, Search } from "./style";

// components
import Logo from "@ui/Logo";
import MenuButton from "@ui/MenuButton";
import ShapeButton from "@ui/ShapeButton";
import { motion } from "framer-motion";
import CurrentUser from "@layout/Panel/CurrentUser";

// hooks
import useWindowSize from "@hooks/useWindowSize";
import usePanelScroll from "@hooks/usePanelScroll";
import { useSidebarContext } from "@contexts/sidebarContext";
import { useRef, useEffect } from "react";
import { Box, Popover, Typography } from "@mui/material";
import { useState } from "react";
import styled from "styled-components";
import theme from "styled-theming";
import { colors, dark, flex, light } from "@styles/vars";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import NotificationItem from "@ui/NotificationItem";

const bg = theme("theme", {
  light: light.bodyBg,
  dark: dark.highlight,
});

export const Button = styled.button`
  width: 40px;
  aspect-ratio: 1;
  background-color: ${bg};
  color: ${colors.gray};
  ${flex.col}
  ${flex.center}
  position: relative;
  transition: color var(--transition), background-color var(--transition);

  &:hover,
  &:focus {
    background-color: ${colors.blue};
    color: #fff;
  }

  .badge {
    position: absolute;
  }

  &.square {
    border-radius: 8px;
  }

  &.round {
    border-radius: 50%;
  }
`;

const NotificationsContainerBg = theme("theme", {
  light: light.widgetBg,
  dark: dark.widgetBg,
});

const NotificationsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0.7rem;
  background-color: ${NotificationsContainerBg};
  min-height: 40px;
  max-height: 400px;
  overflow-y: auto;
  width: 100%;
  min-width: 400px;
`;

const Panel = () => {
  const { width } = useWindowSize();
  const isMobile = width < 768;
  const isDesktop = width >= 1366;
  const classname = usePanelScroll();
  const { isSidebarOpen } = useSidebarContext();
  const headerRef = useRef(null);

  const navigate = useNavigate();

  const { notifications } = useSelector((state) => state.notifications);

  useEffect(() => {
    document.documentElement.style.setProperty("--header-height", `${headerRef.current.offsetHeight}px`);
  }, [width]);

  const [anchorEl, setAnchorEl] = useState(null);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);
  const id = open ? "simple-popover" : undefined;

  return (
    <>
      <Header
        as={motion.header}
        animate={{ y: isSidebarOpen && isMobile ? "-100%" : 0 }}
        transition={{ duration: 0.3, ease: "linear", type: "tween" }}
        className={classname}
        ref={headerRef}
      >
        {!isDesktop && (
          <div className="logo-wrapper">
            <Logo compact={isMobile} />
          </div>
        )}
        {/* <Search>
        <Input type="search" id="globalSearch" placeholder={width < 414 ? "Search" : "Search patients or doctors"} />
        <Label htmlFor="globalSearch">
          <i className="icon icon-search"></i>
        </Label>
        </Search> */}
        {isMobile ? (
          <>
            <CurrentUser />
            <MenuButton />
          </>
        ) : (
          <Actions>
            
            <CurrentUser />
            {width < 1366 && <MenuButton />}
          </Actions>
        )}
      </Header>

      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{ horizontal: "right" }}
        sx={{ mt: { xs: 3, lg: 0 } }}
      >
        <NotificationsContainer>
          {notifications?.length > 0 ? (
            notifications?.map((item, index) => <NotificationItem key={index} notification={item} />)
          ) : (
            <Typography sx={{ width: "fit-content", mx: "auto" }}>No notification yet..</Typography>
          )}
        </NotificationsContainer>
      </Popover>
    </>
  );
};

export default Panel;
