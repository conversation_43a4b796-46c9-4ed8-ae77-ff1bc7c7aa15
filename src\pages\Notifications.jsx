import Widget from "@components/Widget";
import { db } from "@config/firebase.config";
import { COLLECTIONS } from "@constants/app";
import { zodResolver } from "@hookform/resolvers/zod";
import Page from "@layout/Page";
import {
  Box,
  Button,
  CircularProgress,
  FormControl,
  FormControlLabel,
  Grid,
  Radio,
  RadioGroup,
  Typography,
} from "@mui/material";
import { breakpoints, dark, light, textSizes } from "@styles/vars";
import Field from "@ui/Field";
import NotificationItem from "@ui/NotificationItem";
import TextArea from "@ui/TextArea/TextArea";
import { addDoc, collection, Timestamp } from "firebase/firestore";
import { useSnackbar } from "notistack";
import React from "react";
import { Controller, useForm } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";
import styled from "styled-components";
import theme from "styled-theming";
import { z } from "zod";

const notificationSchema = z.object({
  role: z.enum(["ALL", "NURSE", "CAREGIVER", "CLIENT"], { message: "Select you target role" }).default("ALL"),
  title: z.string("Title is required").nonempty("Title is required").min(5, "Title must have 5 chararcters atleast"),
  body: z.string("Body is required").nonempty("Body is required").min(5, "Body must have 5 chararcters atleast"),
});

const StyledField = styled(Field)`
  width: 100%;
`;

const Label = styled.label`
  font-size: ${textSizes["14"]};
  width: fit-content;
  margin-bottom: 8px;
  display: block;
`;

const NotificationsContainerBg = theme("theme", {
  light: light.widgetBg,
  dark: dark.widgetBg,
});

const NotificationsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0.7rem;
  background-color: ${NotificationsContainerBg};
  width: 100%;

  ${breakpoints.mobileL} {
    padding-left: 0;
    padding-right: 0;
  }
`;

const Notifications = () => {
  const { enqueueSnackbar } = useSnackbar();
  const { user } = useSelector((state) => state.auth);
  const dispatch = useDispatch();
  const { notifications } = useSelector((state) => state.notifications);

  const {
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
    watch,
    setValue,
    reset,
  } = useForm({
    defaultValues: {
      role: "ALL",
      title: "",
      body: "",
    },
    resolver: zodResolver(notificationSchema),
  });

  async function submitFormToSendNotfication(formValues) {
    const payload = {
      ...formValues,
      type: "SENT_BY_ADMIN",
      metadata: {
        adminId: user?.id,
      },
      createdAt: Timestamp.now(),
    };
    
    
    try {
      const docRef = await addDoc(collection(db, COLLECTIONS.NOTIFICATIONS), payload);
      
      // Don't manually add to Redux - let the real-time listener handle it
      // The subscribeToNotifications listener will automatically add this notification
      
      enqueueSnackbar("Notification sent successfully", { variant: "success" });
      reset();
    } catch (error) {
      console.error("Error sending notification:", error);
      enqueueSnackbar("Couldn't send the notification", { variant: "error" });
    }
  }

  return (
    <>
      <Page title="Notifications">
        <Widget>
          <Grid container spacing={2} sx={{ p: { xs: 2, sm: 3 } }}>
            {/* LIST OF NOTIFICATIONS */}
            <Grid size={{ xs: 12, md: 6 }} sx={{ maxHeight: 470, overflowY: "auto" }} order={{ xs: 2, md: 1 }}>
              <NotificationsContainer>
                {notifications?.length > 0 ? (
                  notifications?.map((item, index) => <NotificationItem key={index} notification={item} />)
                ) : (
                  <Typography sx={{ width: "fit-content", mx: "auto" }}>No notification yet..</Typography>
                )}
              </NotificationsContainer>
            </Grid>

            {/* FORM: ADMIN ONLY */}
            {user?.role === "ADMIN" ? (
              <Grid size={{ xs: 12, md: 6 }} order={{ xs: 1, md: 2 }}>
                <Box
                // sx={{ pt: { xs: 2, sm: 4 }, pr: { xs: 2, sm: 4 } }}
                >
                  <form onSubmit={handleSubmit(submitFormToSendNotfication)}>
                    <Box mb={2}>
                      <FormControl>
                        <RadioGroup
                          aria-labelledby="demo-radio-buttons-group-label"
                          value={watch("role")}
                          onChange={(e) => setValue("role", e.target?.value)}
                          name="radio-buttons-group"
                          row
                        >
                          <FormControlLabel value="ALL" control={<Radio />} label="All" />
                          <FormControlLabel value="NURSE" control={<Radio />} label="Nurses" />
                          <FormControlLabel value="CAREGIVER" control={<Radio />} label="Caregivers" />
                          <FormControlLabel value="CLIENT" control={<Radio />} label="Patients" />
                        </RadioGroup>
                      </FormControl>
                    </Box>

                    <Box mb={2}>
                      <Controller
                        name="title"
                        control={control}
                        render={({ field }) => {
                          return (
                            <>
                              <Label htmlFor="title">Title</Label>
                              <StyledField type="text" id="title" {...field} placeholder="Title of the notification" />
                            </>
                          );
                        }}
                      />
                      {errors?.title?.message && (
                        <Typography color="error" variant="caption">
                          {errors?.title?.message}
                        </Typography>
                      )}
                    </Box>

                    <Box mb={2}>
                      <Controller
                        name="body"
                        control={control}
                        render={({ field }) => {
                          return (
                            <>
                              <Label htmlFor="body">Body</Label>
                              <TextArea type="text" id="body" {...field} placeholder="Body of the notification" />
                            </>
                          );
                        }}
                      />
                      {errors?.body?.message && (
                        <Typography color="error" variant="caption">
                          {errors?.body?.message}
                        </Typography>
                      )}
                    </Box>

                    <Box sx={{ display: "flex", justifyContent: "flex-end", alignItems: "center" }}>
                      <Button
                        variant="contained"
                        color="primary"
                        type="submit"
                        sx={{
                          borderRadius: 2,
                          py: 1,
                          textTransform: "none",
                          width: "100%",
                          maxWidth: "200px",
                        }}
                        disabled={isSubmitting}
                      >
                        {"Send"}
                        {isSubmitting ? <CircularProgress size={16} color="inherit" sx={{ marginLeft: 1 }} /> : null}
                      </Button>
                    </Box>
                  </form>
                </Box>
              </Grid>
            ) : null}
          </Grid>
        </Widget>
      </Page>
    </>
  );
};

export default Notifications;
