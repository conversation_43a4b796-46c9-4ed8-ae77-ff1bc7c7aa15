import { StyledForm } from "@widgets/UserSettings/style";
import Field from "@ui/Field";
import CustomSelect from "@ui/Select";
import { Button, CircularProgress, Divider, Grid, IconButton } from "@mui/material";
import { Close } from "@mui/icons-material";
import { VisibilityOff, Visibility } from "@mui/icons-material";

// hooks
import { useSearchParams } from "react-router-dom";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Box, Typography } from "@mui/material";
import styled from "styled-components";
import { textSizes } from "@styles/vars";
import { useDispatch, useSelector } from "react-redux";
import { useEffect } from "react";
import { COLLECTIONS, COUNTRY_CODE, FOLDERS } from "@constants/app";
import { addNewUserAction, updateUserAction } from "@store/slices/users";
import { useSnackbar } from "notistack";
import { useState } from "react";
import { convertToDataURL, generateNames, generateRandomString } from "@utils/helpers";
import { deleteFileFromStorage, uploadImageToFirebase } from "@utils/storage";
import { firebaseService } from "service/firebase.service";
import DateInput from "@components/MaskedInputs/Date";
import Phone from "@components/MaskedInputs/Phone";
import PhoneWithCountryPicker from "@components/MaskedInputs/PhoneWithCountryPicker";
import moment from "moment";
import { doc, Timestamp, updateDoc } from "firebase/firestore";
import { db } from "../config/firebase.config";
import GooglePlacesAutocomplete, { geocodeByPlaceId } from "react-google-places-autocomplete";

const Label = styled.label`
  font-size: ${textSizes["14"]};
  width: fit-content;
  margin-bottom: 8px;
  display: block;
`;

const DocPreview = styled.div`
  position: relative;
  margin-top: 8px;
  border-radius: 8px;
  border: 1px solid lightgrey;
  img {
    height: 60px;
    width: 100px;
    display: block;
    border-radius: 8px;
  }
  button {
    position: absolute;
    display: inline-block;
    top: 0;
    right: 0;
    z-index: 10;
    background-color: rgba(75, 75, 75, 0.39);
    height: 24px;
    width: 24px;
    border-radius: 99px;
  }
`;

const StyledField = styled(Field)`
  width: 100%;
`;

const GENDER_SELECT = [
  { label: "Male", value: "male" },
  { label: "Female", value: "female" },
];

const clientSchema = z
  .object({
    email: z.string().nonempty("Email is required").email("Must be valid email"),
    password: z.string().optional(),
    name: z.string().min(3, "Must be at least 3 characters"),
    gender: z.enum(["male", "female"], {
      errorMap: () => ({ message: "Gender must be either male or female" }),
    }),
    dob: z.string().nonempty("Date of birth is required"),
    phone: z.string().nonempty("Phone is required").min(10, "Phone number is too short"),
    address: z
      .object({
        formattedAddress: z.string().nonempty("Address is required"),
        placeId: z.string().nonempty("placeId is required"),
        country: z.string().optional(),
        countryCode: z.string().optional(),
        city: z.string().optional(),
        lat: z.number(),
        lng: z.number(),
      })
      .optional(),
    photo: z.string().optional(),
    assignedNurse: z.string().nonempty("Nurse must be assigned"),
    emergencyContactPerson: z.object({
      name: z.string().nonempty("Contact Person Name is required"),
      phone: z.string().nonempty("Contact Person Phone is required").min(10, "Phone number is too short"),
    }),
    physician: z.object({
      name: z.string().optional().default(""),
      email: z.string().optional().default(""),
    }),
    documents: z.array(z.string()).optional().default([]),
    mode: z.string().optional(),
  })
  .refine(
    (data) => {
      if (data.mode === "add") {
        return data.password && data.password.length >= 6;
      }
      return true;
    },
    {
      message: "Password is required and must be at least 6 characters",
      path: ["password"],
    },
  )
  .refine(
    (data) => {
      // Time dependency validation
      if (data.fromText && !data.toText) {
        return false;
      }
      if (!data.fromText && data.toText) {
        return false;
      }
      return true;
    },
    {
      message: "Both start and end times are required",
      path: ["toText"],
    },
  )
  .refine(
    (data) => {
      // Time order validation (only if both exist)
      if (data.fromText && data.toText) {
        const fromMoment = moment(data.fromText, "HH:mm");
        const toMoment = moment(data.toText, "HH:mm");
        return toMoment.isAfter(fromMoment);
      }
      return true;
    },
    {
      message: "End time must be after start time",
      path: ["toText"],
    },
  );

const MAX_DATE_DOB = new Date();
MAX_DATE_DOB.setFullYear(new Date().getFullYear() - 5);
const RANDOM_PASSWORD = `insyt@${generateRandomString(4)}`;

const AddPatienStep1 = ({ gotoNext, goBack, canGoBack, setCurrentPatient, currentPatient }) => {
  const [showPassword, setShowPassword] = useState(false);
  const { enqueueSnackbar } = useSnackbar();
  const [searchParams] = useSearchParams();
  const client_id = searchParams.get("client_id");

  const dispatch = useDispatch();
  const { nurses } = useSelector((state) => state.users);

  const [placeVal, setPlaceVal] = useState(null);
  const {
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
    setValue,
    setError,
    watch,
  } = useForm({
    defaultValues: {
      email: "",
      password: RANDOM_PASSWORD,
      name: "",
      gender: "",
      dob: "",
      phone: "",
      address: {},
      assignedNurse: "",
      emergencyContactPerson: { name: "", phone: "" },
      physician: { name: "", email: "" },
      documents: [],
      photo: "",
      mode: "add",
    },
    resolver: zodResolver(clientSchema),
    mode: "all",
  });

  const nurses_list = nurses?.map((item) => ({ label: item?.name, value: item.id }));

  // * COPY CREDENTIALS
  async function onCopyCredentials() {
    const name = watch("name");
    const email = watch("email");
    const password = watch("password");
    const credentialsText = `Name: ${name}\nEmail: ${email}\nPassword: ${password}`;
    try {
      await navigator.clipboard.writeText(credentialsText);
      enqueueSnackbar("Credentials copied to clipboard", { variant: "success" });
    } catch (error) {
      enqueueSnackbar("Failed to copy credentials", { variant: "error" });
    }
  }

  // * PROVIDES VALUE FOR CREDENTIAL COPYING BUTTON DISABILITY
  function isCopyBtnDisabled() {
    const name = watch("name");
    const email = watch("email");
    const password = watch("password");
    const hasErrors = errors.name || errors.email || errors.password;
    const hasEmptyFields = !name || !email || !password;
    return hasErrors || hasEmptyFields;
  }

  // * ON SELECT DOCUMENTS
  async function onSelectDocuments(files) {
    if (files?.length > 0) {
      const arr_promises = Array.from(files).map((file) => convertToDataURL(file));
      const arr_data_urls = await Promise.all(arr_promises);
      const new_documents = [...watch("documents"), ...arr_data_urls];
      setValue("documents", [...new Set(new_documents)]);
    }
  }

  // * ON DELETE DOCUMENT
  function onDeleteDocument(val) {
    const arr = watch("documents")?.filter((item) => item !== val);
    setValue("documents", arr);
  }

  // * ON FORM SUBMISSION
  async function submitForm(formValues) {
    // * UPDATE CLIENT
    if (currentPatient?.id) {
      const { name, gender, dob, phone, address, assignedNurse, emergencyContactPerson, physician } = formValues;
      const payload = {
        id: currentPatient?.id,
        role: "CLIENT",
        name,
        gender,
        dob,
        phone: phone,
        address,
        assignedNurse,
        emergencyContactPerson: {
          name: emergencyContactPerson?.name,
          phone: emergencyContactPerson?.phone,
        },
        physician: {
          name: physician?.name,
          email: physician?.email,
        },
        updatedAt: Timestamp.now(),
        onboardPercentage: currentPatient?.onboardPercentage || 25,
      };
      try {
        await updateDoc(doc(db, COLLECTIONS.USERS, currentPatient?.id), payload)
          .then(() => {
            enqueueSnackbar("Patient updated successfully", { variant: "success" });
            const tranformed_client = {
              ...payload,
              firstName: generateNames(name)?.firstName,
              lastName: generateNames(name)?.lastName,
              reg: new Date(),
              age: new Date().getFullYear() - new Date(dob)?.getFullYear(),
            };
            dispatch(updateUserAction(tranformed_client));
            setCurrentPatient(tranformed_client);
            gotoNext();
          })
          .catch((error) => {
            enqueueSnackbar("Couldn't update the patient", { variant: "error" });
          });
      } catch (error) {
        console.error("ERROR UPDATE PAITENT", error);
        enqueueSnackbar("Error while updating patient", { variant: "error" });
      }
    }

    // * ADD CLIENT
    else {
      const {
        email,
        password,
        name,
        gender,
        dob,
        phone,
        address,
        assignedNurse,
        emergencyContactPerson,
        physician,
        documents,
      } = formValues;
      const payload = {
        email: email?.toLowerCase(),
        password,
        name,
        gender,
        dob,
        phone: phone,
        address,
        emergencyContactPerson: {
          name: emergencyContactPerson?.name,
          phone: emergencyContactPerson?.phone,
        },
        assignedNurse,
        isActive: true,
        status: "ACTIVE",
        physician,
        photo: { path: "", url: "" },
        role: "CLIENT",
        onboardPercentage: 25,
        isOnboarded: false,
      };

      try {
        if (documents?.length > 0) {
          const arr_promises = documents?.map((item) => uploadImageToFirebase(FOLDERS.CLIENT_DOCS, item));
          const arr_urls = await Promise.all(arr_promises);
          payload.documents = arr_urls;
        }

        await firebaseService
          .createUser(payload)
          .then(({ data }) => {
            enqueueSnackbar("New client added", { variant: "success" });
            const tranformed_client = {
              ...data.user,
              firstName: generateNames(data?.user?.name)?.firstName,
              lastName: generateNames(data?.user?.name)?.lastName,
              reg: new Date(),
              age: new Date().getFullYear() - new Date(data?.user?.dob)?.getFullYear(),
            };
            dispatch(addNewUserAction(tranformed_client));
            setCurrentPatient(tranformed_client);
            gotoNext();
          })
          .catch((error) => {
            console.error("Firebase service error:", error);
            if (error?.response?.data?.code === "auth/email-already-exists") {
              enqueueSnackbar("Email is already taken", { variant: "error" });
            }
            if (payload?.carePlan?.url) {
              deleteFileFromStorage(payload?.carePlan?.url);
            }
          });
      } catch (error) {
        console.error("Top level error:", error);
        enqueueSnackbar("Couldn't add new client", { variant: "error" });
      }
    }
  }

  useEffect(() => {
    if (currentPatient) {
      setValue("mode", "edit");
      setValue("name", currentPatient?.name);
      setValue("email", currentPatient?.email);
      setValue("gender", currentPatient?.gender);
      setValue("dob", currentPatient?.dob);
      setValue("phone", currentPatient?.phone || "");
      setValue("address", {
        formattedAddress: currentPatient?.address?.formattedAddress || "",
        placeId: currentPatient?.address?.placeId || "",
        lat: currentPatient?.address?.lat || 0,
        lng: currentPatient?.address?.lng || 0,
        city: currentPatient?.address?.city || "",
        country: currentPatient?.address?.country || "",
        countryCode: currentPatient?.address?.countryCode || "",
      });
      setValue("assignedNurse", currentPatient?.assignedNurse);
      setValue("carePlan", currentPatient?.carePlan?.url);
      setValue("emergencyContactPerson.name", currentPatient?.emergencyContactPerson?.name);
      setValue("emergencyContactPerson.phone", currentPatient?.emergencyContactPerson?.phone || "");
      setValue("physician.name", currentPatient?.physician?.name);
      setValue("physician.email", currentPatient?.physician.email);
      setValue("photo", currentPatient?.photo?.url);
    }
  }, [currentPatient]);

  useEffect(() => {
    async function fetchPlaceInfo() {
      if (placeVal) {
        const [res] = await geocodeByPlaceId(placeVal?.value?.place_id);
        if (res) {
          const country = res?.address_components?.find((item) => item.types.includes("country"));
          const city = res?.address_components?.find((item) => item.types.includes("locality"));
          const payload = {
            formattedAddress: res.formatted_address,
            lat: res.geometry.location.lat(),
            lng: res.geometry.location.lng(),
            placeId: res.place_id,
            country: country ? country.long_name : "",
            countryCode: country ? country.short_name : "",
            city: city ? city.long_name : "",
          };
          setValue("address", payload);
          setError("address.formattedAddress", { message: undefined });
        }
      }
    }

    fetchPlaceInfo();
  }, [placeVal]);

  return (
    <>
      <StyledForm onSubmit={handleSubmit(submitForm)}>
        <Grid container spacing={2}>
          <Grid size={12} mt={2}>
            <Typography variant="h6">Client Demographics</Typography>
          </Grid>
          {/* EMAIL */}
          <Grid size={{ xs: 12, sm: 6 }} position="relative" height="fit-content">
            <Controller
              name="email"
              control={control}
              render={({ field }) => {
                return (
                  <>
                    <Label htmlFor="email">Email</Label>
                    <StyledField
                      type="text"
                      id="email"
                      {...field}
                      disabled={!!currentPatient?.id}
                      placeholder="Email"
                    />
                  </>
                );
              }}
            />
            {errors?.email?.message && (
              <Typography color="error" variant="caption">
                {errors?.email?.message}
              </Typography>
            )}
          </Grid>

          {/* PASSWORD */}
          {!currentPatient?.id ? (
            <Grid size={{ xs: 12, sm: 6 }} position="relative" height="fit-content">
              <Controller
                name="password"
                control={control}
                render={({ field }) => {
                  return (
                    <>
                      <Label htmlFor="password">Password</Label>
                      <Box position="relative" height="fit-content">
                        <StyledField
                          type={showPassword ? "text" : "password"}
                          id="password"
                          {...field}
                          disabled={!!currentPatient?.id}
                          placeholder="Password"
                        />
                        <IconButton
                          onClick={() => setShowPassword(!showPassword)}
                          sx={{ position: "absolute", top: 0, bottom: 0, right: 2 }}
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </Box>
                    </>
                  );
                }}
              />
              {errors?.password?.message && (
                <Typography color="error" variant="caption">
                  {errors?.password?.message}
                </Typography>
              )}
            </Grid>
          ) : (
            <Grid size={{ xs: 12, sm: 6 }}></Grid>
          )}

          {/* NAME */}
          <Grid size={{ xs: 12, sm: 6 }} position="relative" height="fit-content">
            <Controller
              name="name"
              control={control}
              render={({ field }) => {
                return (
                  <>
                    <Label htmlFor="name">Full Name</Label>
                    <StyledField type="text" id="name" {...field} placeholder="Full Name" />
                  </>
                );
              }}
            />
            {errors?.name?.message && (
              <Typography color="error" variant="caption">
                {errors?.name?.message}
              </Typography>
            )}
          </Grid>

          {/* GENDER */}
          <Grid size={{ xs: 12, sm: 6 }} position="relative" height="fit-content">
            <Controller
              name="gender"
              control={control}
              render={({ field }) => (
                <>
                  <Label htmlFor="gender">Gender</Label>
                  <CustomSelect
                    options={GENDER_SELECT}
                    variant="basic"
                    value={GENDER_SELECT.find((opt) => opt.value === field.value) || null}
                    changeHandler={(selected) => field.onChange(selected?.value)}
                    placeholder="Select Gender"
                  />
                  {errors?.gender?.message && (
                    <Typography color="error" variant="caption">
                      {errors?.gender?.message}
                    </Typography>
                  )}
                </>
              )}
            />
          </Grid>

          {/* DOB */}
          <Grid size={{ xs: 12, sm: 6 }} position="relative" height="fit-content">
            <Controller
              name="dob"
              control={control}
              render={({ field }) => (
                <>
                  <Label htmlFor="dob">Date of Birth</Label>
                  <DateInput
                    id="dob"
                    maxDate={MAX_DATE_DOB}
                    onChange={(val) => {
                      const formatted_val = moment(val).format("YYYY-MM-DD");
                      setValue("dob", formatted_val);
                      setError("dob", { message: undefined });
                    }}
                    value={watch("dob")}
                  />
                  {errors?.dob?.message && (
                    <Typography color="error" variant="caption">
                      {errors?.dob?.message}
                    </Typography>
                  )}
                </>
              )}
            />
          </Grid>

          {/* PHONE */}
          <Grid size={{ xs: 12, sm: 6 }} position="relative" height="fit-content">
            <Controller
              name="phone"
              control={control}
              render={({ field }) => {
                return (
                  <>
                    <Label htmlFor="phone">Phone</Label>
                    <PhoneWithCountryPicker
                      id="phone"
                      value={field.value}
                      onChange={field.onChange}
                      placeholder="Enter phone number"
                    />
                  </>
                );
              }}
            />
            {errors?.phone?.message && (
              <Typography color="error" variant="caption">
                {errors?.phone?.message}
              </Typography>
            )}
          </Grid>

          {/* ADDRESS */}
          <Grid size={{ xs: 12, sm: 6 }} position="relative" height="fit-content">
            <Controller
              name="address"
              control={control}
              render={({ field }) => (
                <>
                  <Label htmlFor="address">Address</Label>
                  <GooglePlacesAutocomplete
                    apiKey={process.env.REACT_APP_GOOGLE_MAP_KEY}
                    selectProps={{
                      defaultValue: "",
                      value: placeVal,
                      onChange: setPlaceVal,
                      id: "address",
                    }}
                  />

                  {errors?.address?.formattedAddress?.message && (
                    <Typography color="error" variant="caption">
                      {errors?.address?.formattedAddress?.message}
                    </Typography>
                  )}
                </>
              )}
            />
          </Grid>

          {/* ASSIGN NURSE */}
          <Grid size={{ xs: 12, sm: 6 }} position="relative" height="fit-content">
            <Controller
              name="assignedNurse"
              control={control}
              render={({ field }) => (
                <>
                  <Label htmlFor="gender">Assign Nurse</Label>
                  <CustomSelect
                    options={nurses_list}
                    key={watch("nurse")}
                    variant="basic"
                    value={nurses_list.find((opt) => opt.value === field.value) || null}
                    changeHandler={(selected) => {
                      field.onChange(selected?.value);
                    }}
                    placeholder="Assign Nurse"
                  />
                  {errors?.assignedNurse?.message && (
                    <Typography color="error" variant="caption">
                      {errors?.assignedNurse?.message}
                    </Typography>
                  )}
                </>
              )}
            />
          </Grid>

          <Grid size={12}>
            <Divider sx={{ gridColumn: "1 / span 2", marginTop: 2 }} />
          </Grid>
          <Grid size={12}>
            <Typography variant="h6">Emergency Contact Person</Typography>
          </Grid>

          {/* NAME */}
          <Grid size={{ xs: 12, sm: 6 }} position="relative" height="fit-content">
            <Controller
              name="emergencyContactPerson.name"
              control={control}
              render={({ field }) => {
                return (
                  <>
                    <Label htmlFor="contactPersonName">Contact Person Name</Label>
                    <StyledField type="text" id="contactPersonName" {...field} placeholder="Contact Person Name" />
                  </>
                );
              }}
            />
            {errors?.emergencyContactPerson?.name?.message && (
              <Typography color="error" variant="caption">
                {errors?.emergencyContactPerson?.name?.message}
              </Typography>
            )}
          </Grid>

          {/* EMERGENCY CONTACT */}
          <Grid size={{ xs: 12, sm: 6 }} position="relative" height="fit-content">
            <Controller
              name="emergencyContactPerson.phone"
              control={control}
              render={({ field }) => {
                return (
                  <>
                    <Label htmlFor="emergencyContactPerson">Contact Person Phone</Label>
                    <PhoneWithCountryPicker
                      id="emergencyContactPerson"
                      value={field.value}
                      onChange={field.onChange}
                      placeholder="Enter contact person phone"
                    />
                  </>
                );
              }}
            />
            {errors?.emergencyContactPerson?.phone?.message && (
              <Typography color="error" variant="caption">
                {errors?.emergencyContactPerson?.phone?.message}
              </Typography>
            )}
          </Grid>

          <Grid size={12}>
            <Divider sx={{ marginTop: 2 }} />
          </Grid>
          <Grid size={12}>
            <Typography variant="h6">Primary Physician Info</Typography>
          </Grid>

          {/* PHYSICIAN NAME */}
          <Grid size={{ xs: 12, sm: 6 }} position="relative" height="fit-content">
            <Controller
              name="physician.name"
              control={control}
              render={({ field }) => {
                return (
                  <>
                    <Label htmlFor="physicianName">Physician Name</Label>
                    <StyledField type="text" id="physicianName" {...field} placeholder="Physician Name" />
                  </>
                );
              }}
            />
          </Grid>

          {/* PHYSICIAN EMAIL */}
          <Grid size={{ xs: 12, sm: 6 }} position="relative" height="fit-content">
            <Controller
              name="physician.email"
              control={control}
              render={({ field }) => {
                return (
                  <>
                    <Label htmlFor="physicianEmail">Physician Email</Label>
                    <StyledField type="text" id="physicianEmail" {...field} placeholder="Physician Email" />
                  </>
                );
              }}
            />
            {errors?.physician?.email?.message && (
              <Typography color="error" variant="caption">
                {errors?.physician?.email?.message}
              </Typography>
            )}
          </Grid>

          {!currentPatient?.id && (
            <>
              <Grid size={12}>
                <Divider sx={{ marginTop: 2 }} />
              </Grid>
              <Grid size={12}>
                <Typography variant="h6">Documents (ID, clinical records, etc.)</Typography>
              </Grid>

              {/* DOCUMENTS */}
              <Grid size={{ xs: 12, sm: 6 }} position="relative" height="fit-content">
                <StyledField
                  type="file"
                  accept=".png,.jpg,.jpeg"
                  id="documents"
                  placeholder="Documents (ID, clinical records, etc.)"
                  onChange={(e) => onSelectDocuments(e?.target?.files)}
                  multiple
                />
              </Grid>
              {watch("documents").length ? (
                <Grid size={12} mt={1} display="flex" flexWrap="wrap" gap={1}>
                  {watch("documents")?.map((item, index) => (
                    <DocPreview key={index}>
                      <img src={item} />
                      <button onClick={() => onDeleteDocument(item)} type="button">
                        <Close />
                      </button>
                    </DocPreview>
                  ))}
                </Grid>
              ) : null}
            </>
          )}

          {!currentPatient?.id && (
            <Grid size={12} mt={2} display="flex" justifyContent="end">
              <Button
                variant="contained"
                color="secondary"
                type="button"
                sx={{
                  fontSize: 16,
                  borderRadius: 2,
                  textTransform: "none",
                  fontWeight: 400,
                  ":hover": { color: "#000" },
                }}
                onClick={onCopyCredentials}
                disabled={isCopyBtnDisabled()}
              >
                {"Copy Credentials"}
              </Button>
            </Grid>
          )}

          <Grid
            size={12}
            sx={{
              display: "flex",
              marginTop: 2,
              justifyContent: "space-between",
              alignItems: "center",
              gap: 1.5,
            }}
          >
            <Button
              variant="outlined"
              color="primary"
              type="button"
              sx={{
                fontSize: 16,
                borderRadius: 2,
                textTransform: "none",
                fontWeight: 400,
                maxWidth: 200,
                width: "100%",
              }}
              disabled={!canGoBack || isSubmitting}
              onClick={goBack}
            >
              {"Back"}
            </Button>
            <Button
              variant="contained"
              color="primary"
              type="submit"
              sx={{
                fontSize: 16,
                borderRadius: 2,
                textTransform: "none",
                fontWeight: 400,
                maxWidth: 200,
                width: "100%",
              }}
              disabled={isSubmitting}
            >
              {"Next"}
              {isSubmitting ? <CircularProgress size={16} color="inherit" sx={{ marginLeft: 1 }} /> : null}
            </Button>
          </Grid>
        </Grid>
      </StyledForm>
    </>
  );
};

export default AddPatienStep1;
