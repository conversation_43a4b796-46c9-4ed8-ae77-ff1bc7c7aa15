import React, { useState } from "react";
import { Box, Typography, Paper } from "@mui/material";
import PhoneWithCountryPicker from "@components/MaskedInputs/PhoneWithCountryPicker";

const PhonePickerDemo = () => {
  const [phoneValue, setPhoneValue] = useState("");

  return (
    <Paper sx={{ p: 3, m: 2, maxWidth: 600 }}>
      <Typography variant="h5" gutterBottom>
        Phone Country Picker Demo
      </Typography>
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Select a country and enter a phone number:
        </Typography>
        <PhoneWithCountryPicker
          id="demo-phone"
          value={phoneValue}
          onChange={setPhoneValue}
          placeholder="Enter phone number"
        />
      </Box>
      
      <Box sx={{ mt: 2, p: 2, bgcolor: "grey.100", borderRadius: 1 }}>
        <Typography variant="body2">
          <strong>Current Value:</strong> {phoneValue || "No phone number entered"}
        </Typography>
      </Box>
      
      <Box sx={{ mt: 2 }}>
        <Typography variant="body2" color="text.secondary">
          Features:
        </Typography>
        <ul style={{ margin: "8px 0", paddingLeft: "20px" }}>
          <li>Country selection with flags</li>
          <li>Automatic phone formatting based on country</li>
          <li>Support for all major countries</li>
          <li>Proper validation and parsing</li>
        </ul>
      </Box>
    </Paper>
  );
};

export default PhonePickerDemo;
