import React, { useState, useEffect } from "react";
import { Box, MenuItem, Select, FormControl, InputAdornment } from "@mui/material";
import { PatternFormat } from "react-number-format";
import Field from "@ui/Field";
import { COUNTRIES, DEFAULT_COUNTRY, getCountryByPhoneCode } from "@constants/countries";
import styled from "styled-components";

const CountrySelect = styled(Select)`
  && {
    min-width: 120px;
    margin-right: 8px;
    
    .MuiSelect-select {
      padding: 8px 12px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .MuiOutlinedInput-notchedOutline {
      border-color: #e0e0e0;
    }
    
    &:hover .MuiOutlinedInput-notchedOutline {
      border-color: #b0b0b0;
    }
    
    &.Mui-focused .MuiOutlinedInput-notchedOutline {
      border-color: #1976d2;
    }
  }
`;

const PhoneContainer = styled(Box)`
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
`;

const StyledField = styled(Field)`
  flex: 1;
`;

const FlagText = styled.span`
  font-size: 18px;
  margin-right: 4px;
`;

const PhoneCodeText = styled.span`
  font-size: 14px;
  color: #666;
`;

const PhoneWithCountryPicker = ({ 
  id, 
  placeholder, 
  onChange, 
  value = "", 
  defaultCountry = DEFAULT_COUNTRY,
  disabled = false,
  ...props 
}) => {
  const [selectedCountry, setSelectedCountry] = useState(defaultCountry);
  const [phoneNumber, setPhoneNumber] = useState("");

  // Parse existing value to extract country and phone number
  useEffect(() => {
    if (value && typeof value === "string") {
      // Try to find country by matching phone code at the start of the value
      const matchedCountry = COUNTRIES.find(country => 
        value.startsWith(country.phoneCode)
      );
      
      if (matchedCountry) {
        setSelectedCountry(matchedCountry);
        // Remove the country code from the phone number
        const phoneWithoutCode = value.replace(matchedCountry.phoneCode, "").trim();
        setPhoneNumber(phoneWithoutCode);
      } else {
        // If no country code found, treat as local number
        setPhoneNumber(value);
      }
    } else if (!value) {
      setPhoneNumber("");
    }
  }, [value]);

  const handleCountryChange = (event) => {
    const countryCode = event.target.value;
    const country = COUNTRIES.find(c => c.code === countryCode);
    setSelectedCountry(country);
    
    // Update the full phone number with new country code
    const fullPhoneNumber = phoneNumber ? `${country.phoneCode} ${phoneNumber}` : "";
    onChange?.(fullPhoneNumber);
  };

  const handlePhoneChange = (values) => {
    const newPhoneNumber = values.value || "";
    setPhoneNumber(newPhoneNumber);
    
    // Combine country code with phone number
    const fullPhoneNumber = newPhoneNumber ? `${selectedCountry.phoneCode} ${newPhoneNumber}` : "";
    onChange?.(fullPhoneNumber);
  };

  return (
    <PhoneContainer>
      <FormControl size="small">
        <CountrySelect
          value={selectedCountry?.code || DEFAULT_COUNTRY.code}
          onChange={handleCountryChange}
          disabled={disabled}
          displayEmpty
          renderValue={(selected) => {
            const country = COUNTRIES.find(c => c.code === selected);
            return (
              <Box display="flex" alignItems="center">
                <FlagText>{country?.flag}</FlagText>
                <PhoneCodeText>{country?.phoneCode}</PhoneCodeText>
              </Box>
            );
          }}
        >
          {COUNTRIES.map((country) => (
            <MenuItem key={country.code} value={country.code}>
              <Box display="flex" alignItems="center" gap={1}>
                <FlagText>{country.flag}</FlagText>
                <span>{country.name}</span>
                <PhoneCodeText>({country.phoneCode})</PhoneCodeText>
              </Box>
            </MenuItem>
          ))}
        </CountrySelect>
      </FormControl>
      
      <StyledField
        as={PatternFormat}
        id={id}
        placeholder={placeholder || selectedCountry?.format?.replace(/[+\d]/g, 'X') || "XX XXX XXXX"}
        format={selectedCountry?.format?.replace(selectedCountry.phoneCode, "").trim() || "## ### ####"}
        mask="_"
        value={phoneNumber}
        onValueChange={handlePhoneChange}
        disabled={disabled}
        {...props}
      />
    </PhoneContainer>
  );
};

export default PhoneWithCountryPicker;
