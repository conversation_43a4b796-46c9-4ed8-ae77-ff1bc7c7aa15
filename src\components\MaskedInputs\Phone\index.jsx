// components
import Field from "@ui/Field";
import { PatternFormat } from "react-number-format";
import { DEFAULT_COUNTRY } from "@constants/countries";

const Phone = ({
  id,
  placeholder,
  onChange,
  value,
  format = DEFAULT_COUNTRY.format,
  phoneCode = DEFAULT_COUNTRY.phoneCode,
  ...props
}) => {
  return (
    <Field
      as={PatternFormat}
      id={id}
      placeholder={placeholder || `${phoneCode} XX XXX XXXX`}
      format={format}
      mask="_"
      value={value}
      onValueChange={(values) => {
        onChange?.(values.value);
      }}
      {...props}
      style={{ width: "100%" }}
    />
  );
};

export default Phone;
