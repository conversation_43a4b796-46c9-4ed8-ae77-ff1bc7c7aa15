import { db } from "config/firebase.config";
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { collection, doc, getDocs, orderBy, query, setDoc, where } from "firebase/firestore";
import { COLLECTIONS } from "@constants/app";

const initialState = {
  error: null,
  isLoading: false,
  notifications: [],
};

// THUNK TO GET ALL TASKS
export const getNotificationsOfAdmin = createAsyncThunk(
  "notifications/getNotificationsOfAdmin",
  async (_, { rejectWithValue, fulfillWithValue }) => {
    try {
      const q = query(
        collection(db, COLLECTIONS.NOTIFICATIONS),
        where("type", "==", "SENT_BY_ADMIN"),
        orderBy("createdAt", "desc"),
      );
      const snapshot = await getDocs(q);
      const arr = snapshot.docs.map((item) => ({ id: item.id, ...item.data() }));
      return fulfillWithValue(arr);
    } catch (error) {
      console.log("GET NOTIFICATIONS OF ADMIN THUNK >> ", error);
      return rejectWithValue(error);
    }
  },
);

// THUNK TO CREATE NEW TASK DOC
export const getNotificationsOfNurse = createAsyncThunk(
  "notifications/getNotificationsOfNurse",
  async (_, { rejectWithValue, fulfillWithValue }) => {
    try {
      const roles = ["ALL", "NURSE"];

      const queries = roles.map((role) => {
        const q = query(
          collection(db, COLLECTIONS.NOTIFICATIONS),
          where("role", "==", role),
          orderBy("createdAt", "desc"),
        );
        return getDocs(q);
      });
      const snapshots = await Promise.all(queries);
      const merged = snapshots.flatMap((snapshot) => snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() })));
      // merged.sort((a, b) => b.createdAt.toMillis() - a.createdAt.toMillis());
      return fulfillWithValue(merged);
    } catch (error) {
      console.log("GET NOTIFICATIONS OF NURSE THUNK >> ", error);
      return rejectWithValue(error);
    }
  },
);

const notificationSlice = createSlice({
  name: "notifications",
  initialState,
  reducers: {
    addNewNotificationAction: (state, { payload }) => {
      state.notifications = [payload, ...state.notifications];
      return state;
    },
    updateNotificationsRealtime: (state, { payload }) => {
      // Create a map of all notifications (existing + new) to handle deduplication
      const notificationMap = new Map();
      
      // Add existing notifications to the map
      state.notifications.forEach(notification => {
        notificationMap.set(notification.id, notification);
      });
      
      // Add/update with new notifications from the payload
      payload.forEach(notification => {
        notificationMap.set(notification.id, notification);
      });
      
      // Convert map back to array and sort
      state.notifications = Array.from(notificationMap.values())
        .sort((a, b) => b.createdAt?.toMillis() - a.createdAt?.toMillis());
      
      // Update unread count
      state.unreadCount = state.notifications.filter(notification => !notification.read).length;
      return state;
    },
    markNotificationsAsRead: (state) => {
      state.notifications = state.notifications.map(notification => ({
        ...notification,
        read: true
      }));
      state.unreadCount = 0;
      return state;
    },
    setUnreadCount: (state, { payload }) => {
      state.unreadCount = payload;
      return state;
    },
    resetUnreadCount: (state) => {
      state.unreadCount = 0;
    },
    setUnsubscribes: (state, { payload }) => {
      // Ensure payload is an array
      state.unsubscribes = Array.isArray(payload) ? payload : [];
    },
    cleanupSubscriptions: (state) => {
      // Add safety check for undefined/null unsubscribes
      if (state.unsubscribes && Array.isArray(state.unsubscribes)) {
        state.unsubscribes.forEach(unsubscribe => {
          if (typeof unsubscribe === 'function') {
            try {
              unsubscribe();
            } catch (error) {
              console.warn('Error cleaning up subscription:', error);
            }
          }
        });
      }
      state.unsubscribes = [];
    }
  },
  extraReducers: (builder) => {
    builder
      // GET NOTIFICATIONS FOR ADMIN
      .addCase(getNotificationsOfAdmin.pending, (state) => {
        state.error = null;
        state.isLoading = true;
      })
      .addCase(getNotificationsOfAdmin.fulfilled, (state, { payload }) => {
        state.notifications = payload;
        state.isLoading = false;
      })
      .addCase(getNotificationsOfAdmin.rejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      // GET NOTIFICATIONS FOR NURSE
      .addCase(getNotificationsOfNurse.pending, (state) => {
        state.error = null;
        state.isLoading = true;
      })
      .addCase(getNotificationsOfNurse.fulfilled, (state, { payload }) => {
        state.notifications = payload;
        state.isLoading = false;
      })
      .addCase(getNotificationsOfNurse.rejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      });
  },
});

export const { addNewNotificationAction } = notificationSlice.actions;

export default notificationSlice;
