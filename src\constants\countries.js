export const COUNTRIES = [
  {
    name: "Afghanistan",
    code: "AF",
    phoneCode: "+93",
    flag: "🇦🇫",
    format: "+93 ## ### ####"
  },
  {
    name: "Albania",
    code: "AL",
    phoneCode: "+355",
    flag: "🇦🇱",
    format: "+355 ## ### ###"
  },
  {
    name: "Algeria",
    code: "DZ",
    phoneCode: "+213",
    flag: "🇩🇿",
    format: "+213 ## ### ###"
  },
  {
    name: "Argentina",
    code: "AR",
    phoneCode: "+54",
    flag: "🇦🇷",
    format: "+54 ## #### ####"
  },
  {
    name: "Australia",
    code: "AU",
    phoneCode: "+61",
    flag: "🇦🇺",
    format: "+61 # #### ####"
  },
  {
    name: "Austria",
    code: "AT",
    phoneCode: "+43",
    flag: "🇦🇹",
    format: "+43 ### ######"
  },
  {
    name: "Bangladesh",
    code: "BD",
    phoneCode: "+880",
    flag: "🇧🇩",
    format: "+880 #### ######"
  },
  {
    name: "Belgium",
    code: "BE",
    phoneCode: "+32",
    flag: "🇧🇪",
    format: "+32 ### ## ## ##"
  },
  {
    name: "Brazil",
    code: "BR",
    phoneCode: "+55",
    flag: "🇧🇷",
    format: "+55 ## #####-####"
  },
  {
    name: "Canada",
    code: "CA",
    phoneCode: "+1",
    flag: "🇨🇦",
    format: "+1 (###) ###-####"
  },
  {
    name: "China",
    code: "CN",
    phoneCode: "+86",
    flag: "🇨🇳",
    format: "+86 ### #### ####"
  },
  {
    name: "Egypt",
    code: "EG",
    phoneCode: "+20",
    flag: "🇪🇬",
    format: "+20 ## #### ####"
  },
  {
    name: "France",
    code: "FR",
    phoneCode: "+33",
    flag: "🇫🇷",
    format: "+33 # ## ## ## ##"
  },
  {
    name: "Germany",
    code: "DE",
    phoneCode: "+49",
    flag: "🇩🇪",
    format: "+49 ### #######"
  },
  {
    name: "Ghana",
    code: "GH",
    phoneCode: "+233",
    flag: "🇬🇭",
    format: "+233 ## ### ####"
  },
  {
    name: "India",
    code: "IN",
    phoneCode: "+91",
    flag: "🇮🇳",
    format: "+91 ##### #####"
  },
  {
    name: "Indonesia",
    code: "ID",
    phoneCode: "+62",
    flag: "🇮🇩",
    format: "+62 ### #### ####"
  },
  {
    name: "Italy",
    code: "IT",
    phoneCode: "+39",
    flag: "🇮🇹",
    format: "+39 ### ### ####"
  },
  {
    name: "Japan",
    code: "JP",
    phoneCode: "+81",
    flag: "🇯🇵",
    format: "+81 ## #### ####"
  },
  {
    name: "Kenya",
    code: "KE",
    phoneCode: "+254",
    flag: "🇰🇪",
    format: "+254 ### ######"
  },
  {
    name: "Mexico",
    code: "MX",
    phoneCode: "+52",
    flag: "🇲🇽",
    format: "+52 ## #### ####"
  },
  {
    name: "Netherlands",
    code: "NL",
    phoneCode: "+31",
    flag: "🇳🇱",
    format: "+31 ## ### ####"
  },
  {
    name: "Nigeria",
    code: "NG",
    phoneCode: "+234",
    flag: "🇳🇬",
    format: "+234 ## ### ####"
  },
  {
    name: "Pakistan",
    code: "PK",
    phoneCode: "+92",
    flag: "🇵🇰",
    format: "+92 ### #######"
  },
  {
    name: "Philippines",
    code: "PH",
    phoneCode: "+63",
    flag: "🇵🇭",
    format: "+63 ### ### ####"
  },
  {
    name: "Poland",
    code: "PL",
    phoneCode: "+48",
    flag: "🇵🇱",
    format: "+48 ### ### ###"
  },
  {
    name: "Russia",
    code: "RU",
    phoneCode: "+7",
    flag: "🇷🇺",
    format: "+7 ### ### ## ##"
  },
  {
    name: "Saudi Arabia",
    code: "SA",
    phoneCode: "+966",
    flag: "🇸🇦",
    format: "+966 ## ### ####"
  },
  {
    name: "South Africa",
    code: "ZA",
    phoneCode: "+27",
    flag: "🇿🇦",
    format: "+27 ## ### ####"
  },
  {
    name: "South Korea",
    code: "KR",
    phoneCode: "+82",
    flag: "🇰🇷",
    format: "+82 ## #### ####"
  },
  {
    name: "Spain",
    code: "ES",
    phoneCode: "+34",
    flag: "🇪🇸",
    format: "+34 ### ### ###"
  },
  {
    name: "Sweden",
    code: "SE",
    phoneCode: "+46",
    flag: "🇸🇪",
    format: "+46 ## ### ## ##"
  },
  {
    name: "Switzerland",
    code: "CH",
    phoneCode: "+41",
    flag: "🇨🇭",
    format: "+41 ## ### ## ##"
  },
  {
    name: "Turkey",
    code: "TR",
    phoneCode: "+90",
    flag: "🇹🇷",
    format: "+90 ### ### ## ##"
  },
  {
    name: "Ukraine",
    code: "UA",
    phoneCode: "+380",
    flag: "🇺🇦",
    format: "+380 ## ### ## ##"
  },
  {
    name: "United Arab Emirates",
    code: "AE",
    phoneCode: "+971",
    flag: "🇦🇪",
    format: "+971 ## ### ####"
  },
  {
    name: "United Kingdom",
    code: "GB",
    phoneCode: "+44",
    flag: "🇬🇧",
    format: "+44 #### ######"
  },
  {
    name: "United States",
    code: "US",
    phoneCode: "+1",
    flag: "🇺🇸",
    format: "+1 (###) ###-####"
  },
  {
    name: "Vietnam",
    code: "VN",
    phoneCode: "+84",
    flag: "🇻🇳",
    format: "+84 ## #### ####"
  },
  {
    name: "Angola",
    code: "AO",
    phoneCode: "+244",
    flag: "🇦🇴",
    format: "+244 ### ### ###"
  },
  {
    name: "Benin",
    code: "BJ",
    phoneCode: "+229",
    flag: "🇧🇯",
    format: "+229 ## ## ## ##"
  },
  {
    name: "Botswana",
    code: "BW",
    phoneCode: "+267",
    flag: "🇧🇼",
    format: "+267 ## ### ###"
  },
  {
    name: "Burkina Faso",
    code: "BF",
    phoneCode: "+226",
    flag: "🇧🇫",
    format: "+226 ## ## ## ##"
  },
  {
    name: "Cameroon",
    code: "CM",
    phoneCode: "+237",
    flag: "🇨🇲",
    format: "+237 #### ####"
  },
  {
    name: "Chad",
    code: "TD",
    phoneCode: "+235",
    flag: "🇹🇩",
    format: "+235 ## ## ## ##"
  },
  {
    name: "Congo",
    code: "CG",
    phoneCode: "+242",
    flag: "🇨🇬",
    format: "+242 ## ### ####"
  },
  {
    name: "Côte d'Ivoire",
    code: "CI",
    phoneCode: "+225",
    flag: "🇨🇮",
    format: "+225 ## ## ## ##"
  },
  {
    name: "Ethiopia",
    code: "ET",
    phoneCode: "+251",
    flag: "🇪🇹",
    format: "+251 ## ### ####"
  },
  {
    name: "Gabon",
    code: "GA",
    phoneCode: "+241",
    flag: "🇬🇦",
    format: "+241 ## ## ## ##"
  },
  {
    name: "Gambia",
    code: "GM",
    phoneCode: "+220",
    flag: "🇬🇲",
    format: "+220 ### ####"
  },
  {
    name: "Guinea",
    code: "GN",
    phoneCode: "+224",
    flag: "🇬🇳",
    format: "+224 ### ### ###"
  },
  {
    name: "Liberia",
    code: "LR",
    phoneCode: "+231",
    flag: "🇱🇷",
    format: "+231 ## ### ####"
  },
  {
    name: "Mali",
    code: "ML",
    phoneCode: "+223",
    flag: "🇲🇱",
    format: "+223 ## ## ## ##"
  },
  {
    name: "Morocco",
    code: "MA",
    phoneCode: "+212",
    flag: "🇲🇦",
    format: "+212 ###-######"
  },
  {
    name: "Niger",
    code: "NE",
    phoneCode: "+227",
    flag: "🇳🇪",
    format: "+227 ## ## ## ##"
  },
  {
    name: "Rwanda",
    code: "RW",
    phoneCode: "+250",
    flag: "🇷🇼",
    format: "+250 ### ### ###"
  },
  {
    name: "Senegal",
    code: "SN",
    phoneCode: "+221",
    flag: "🇸🇳",
    format: "+221 ## ### ## ##"
  },
  {
    name: "Sierra Leone",
    code: "SL",
    phoneCode: "+232",
    flag: "🇸🇱",
    format: "+232 ## ######"
  },
  {
    name: "Tanzania",
    code: "TZ",
    phoneCode: "+255",
    flag: "🇹🇿",
    format: "+255 ### ### ###"
  },
  {
    name: "Togo",
    code: "TG",
    phoneCode: "+228",
    flag: "🇹🇬",
    format: "+228 ## ## ## ##"
  },
  {
    name: "Tunisia",
    code: "TN",
    phoneCode: "+216",
    flag: "🇹🇳",
    format: "+216 ## ### ###"
  },
  {
    name: "Uganda",
    code: "UG",
    phoneCode: "+256",
    flag: "🇺🇬",
    format: "+256 ### ### ###"
  },
  {
    name: "Zambia",
    code: "ZM",
    phoneCode: "+260",
    flag: "🇿🇲",
    format: "+260 ## ### ####"
  },
  {
    name: "Zimbabwe",
    code: "ZW",
    phoneCode: "+263",
    flag: "🇿🇼",
    format: "+263 ## ### ####"
  }
];

// Default country (Nigeria as it was hardcoded before)
export const DEFAULT_COUNTRY = COUNTRIES.find(country => country.code === "NG");

// Helper function to get country by phone code
export const getCountryByPhoneCode = (phoneCode) => {
  return COUNTRIES.find(country => country.phoneCode === phoneCode);
};

// Helper function to get country by code
export const getCountryByCode = (code) => {
  return COUNTRIES.find(country => country.code === code);
};
